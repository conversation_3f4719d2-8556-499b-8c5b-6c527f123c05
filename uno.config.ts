import { presetUni } from '@uni-helper/unocss-preset-uni'
import {
  defineConfig,
  presetIcons,
  presetAttributify,
  transformerDirectives,
  transformerVariantGroup,
} from 'unocss'

export default defineConfig({
  presets: [
    presetUni(),
    presetIcons({
      scale: 1.2,
      warn: true,
      extraProperties: {
        display: 'inline-block',
        'vertical-align': 'middle',
      },
    }),
    // 支持css class属性化
    presetAttributify(),
  ],
  transformers: [
    // 启用指令功能：主要用于支持 @apply、@screen 和 theme() 等 CSS 指令
    transformerDirectives(),
    // 启用 () 分组功能
    // 支持css class组合，eg: `<div class="hover:(bg-gray-400 font-medium) font-(light mono)">测试 unocss</div>`
    transformerVariantGroup(),
  ],
  shortcuts: [
    {
      center: 'flex justify-center items-center',
    },
  ],
  rules: [
    [
      'p-safe',
      {
        padding:
          'env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left)',
      },
    ],
    ['pt-safe', { 'padding-top': 'env(safe-area-inset-top)' }],
    ['pb-safe', { 'padding-bottom': 'env(safe-area-inset-bottom)' }],
  ],
  theme: {
    colors: {
      /** 主题色，用法如: text-primary */
      primary: 'var(--theme-primary, #2e8b57)',
      /** 主题色浅色，用法如: text-primary-light */
      'primary-light': 'var(--theme-primary-light, #90ee90)',
      /** 主题色深色，用法如: text-primary-dark */
      'primary-dark': 'var(--theme-primary-dark, #006400)',
      /** 辅助色，用法如: text-secondary */
      secondary: 'var(--theme-secondary, #d4af37)',
      /** 辅助色浅色，用法如: text-secondary-light */
      'secondary-light': 'var(--theme-secondary-light, #ffd700)',
      /** 成功色，用法如: text-success */
      success: 'var(--theme-success, #52c41a)',
      /** 警告色，用法如: text-warning */
      warning: 'var(--theme-warning, #faad14)',
      /** 错误色，用法如: text-error */
      error: 'var(--theme-error, #f5222d)',
      /** 信息色，用法如: text-info */
      info: 'var(--theme-info, #1890ff)',
      /** 背景色，用法如: bg-background */
      background: 'var(--theme-background, #fafafa)',
      /** 表面色，用法如: bg-surface */
      surface: 'var(--theme-surface, #ffffff)',
      /** 文字主色，用法如: text-text-primary */
      'text-primary': 'var(--theme-text-primary, #2c3e50)',
      /** 文字次要色，用法如: text-text-secondary */
      'text-secondary': 'var(--theme-text-secondary, #7f8c8d)',
      /** 文字禁用色，用法如: text-text-disabled */
      'text-disabled': 'var(--theme-text-disabled, #bdc3c7)',
      /** 边框色，用法如: border-border */
      border: 'var(--theme-border, #e8e8e8)',
      /** 分割线色，用法如: border-divider */
      divider: 'var(--theme-divider, #f0f0f0)',
    },
    fontSize: {
      /** 提供更小号的字体，用法如：text-2xs */
      '2xs': ['20rpx', '28rpx'],
      '3xs': ['18rpx', '26rpx'],
    },
  },
})
