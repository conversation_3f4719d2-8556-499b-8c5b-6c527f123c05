// @import './iconfont.css';

.test {
  // 可以通过 @apply 多个样式封装整体样式
  @apply mt-4 ml-4;

  padding-top: 4px;
  color: red;
}

:root,
page {
  // 主题色变量 - 默认为翡翠玉石主题
  --theme-primary: #2e8b57;
  --theme-primary-light: #90ee90;
  --theme-primary-dark: #006400;
  --theme-secondary: #d4af37;
  --theme-secondary-light: #ffd700;
  --theme-success: #52c41a;
  --theme-warning: #faad14;
  --theme-error: #f5222d;
  --theme-info: #1890ff;
  --theme-background: #fafafa;
  --theme-surface: #ffffff;
  --theme-text-primary: #2c3e50;
  --theme-text-secondary: #7f8c8d;
  --theme-text-disabled: #bdc3c7;
  --theme-border: #e8e8e8;
  --theme-divider: #f0f0f0;

  // wot-design-uni 主题变量
  --wot-color-theme: var(--theme-primary);
  --wot-button-primary-bg-color: var(--theme-primary);
  --wot-button-primary-color: #ffffff;

  // uni-app 主题变量
  --uni-color-primary: var(--theme-primary);
  --uni-text-color: var(--theme-text-primary);
  --uni-bg-color: var(--theme-background);
  --uni-border-color: var(--theme-border);
}

// 主题相关的通用样式类
.theme-primary {
  color: var(--theme-primary) !important;
}

.theme-primary-bg {
  background-color: var(--theme-primary) !important;
}

.theme-secondary {
  color: var(--theme-secondary) !important;
}

.theme-secondary-bg {
  background-color: var(--theme-secondary) !important;
}

.theme-text-primary {
  color: var(--theme-text-primary) !important;
}

.theme-text-secondary {
  color: var(--theme-text-secondary) !important;
}

.theme-border {
  border-color: var(--theme-border) !important;
}

.theme-surface-bg {
  background-color: var(--theme-surface) !important;
}
