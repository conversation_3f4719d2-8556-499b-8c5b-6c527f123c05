/**
 * 主题配置文件
 * 为玉石、沉香等奢侈品电商小程序设计的主题色方案
 */

export interface ThemeColors {
  /** 主色调 */
  primary: string
  /** 主色调浅色 */
  primaryLight: string
  /** 主色调深色 */
  primaryDark: string
  /** 辅助色 */
  secondary: string
  /** 辅助色浅色 */
  secondaryLight: string
  /** 成功色 */
  success: string
  /** 警告色 */
  warning: string
  /** 错误色 */
  error: string
  /** 信息色 */
  info: string
  /** 背景色 */
  background: string
  /** 表面色 */
  surface: string
  /** 文字主色 */
  textPrimary: string
  /** 文字次要色 */
  textSecondary: string
  /** 文字禁用色 */
  textDisabled: string
  /** 边框色 */
  border: string
  /** 分割线色 */
  divider: string
}

export interface ThemeConfig {
  name: string
  displayName: string
  colors: ThemeColors
  description?: string
}

/**
 * 玉石主题 - 温润如玉，高雅内敛
 * 主色调采用翡翠绿，体现玉石的珍贵与典雅
 */
export const jadeTheme: ThemeConfig = {
  name: 'jade',
  displayName: '翡翠玉石',
  description: '温润如玉，高雅内敛的翡翠绿主题',
  colors: {
    primary: '#2E8B57', // 海绿色，象征翡翠
    primaryLight: '#90EE90', // 浅绿色
    primaryDark: '#006400', // 深绿色
    secondary: '#D4AF37', // 金色，象征贵重
    secondaryLight: '#FFD700', // 浅金色
    success: '#52C41A',
    warning: '#FAAD14',
    error: '#F5222D',
    info: '#1890FF',
    background: '#FAFAFA',
    surface: '#FFFFFF',
    textPrimary: '#2C3E50', // 深灰蓝色，优雅稳重
    textSecondary: '#7F8C8D', // 中灰色
    textDisabled: '#BDC3C7', // 浅灰色
    border: '#E8E8E8',
    divider: '#F0F0F0',
  },
}

/**
 * 沉香主题 - 深沉内敛，古典雅致
 * 主色调采用深棕色，体现沉香的深邃与神秘
 */
export const agarwoodTheme: ThemeConfig = {
  name: 'agarwood',
  displayName: '沉香雅韵',
  description: '深沉内敛，古典雅致的沉香棕主题',
  colors: {
    primary: '#8B4513', // 马鞍棕色，象征沉香
    primaryLight: '#CD853F', // 秘鲁色
    primaryDark: '#654321', // 深棕色
    secondary: '#B8860B', // 暗金色
    secondaryLight: '#DAA520', // 金麒麟色
    success: '#52C41A',
    warning: '#FAAD14',
    error: '#F5222D',
    info: '#1890FF',
    background: '#FDF6E3', // 米色背景，温暖舒适
    surface: '#FFFFFF',
    textPrimary: '#3C2415', // 深棕色文字
    textSecondary: '#8B7355', // 中棕色
    textDisabled: '#C4B5A0', // 浅棕色
    border: '#E6DDD4',
    divider: '#F0EBE5',
  },
}

/**
 * 琥珀主题 - 温暖明亮，奢华典雅
 * 主色调采用琥珀色，体现琥珀的温润与珍贵
 */
export const amberTheme: ThemeConfig = {
  name: 'amber',
  displayName: '琥珀流光',
  description: '温暖明亮，奢华典雅的琥珀金主题',
  colors: {
    primary: '#FF8C00', // 深橙色，象征琥珀
    primaryLight: '#FFB347', // 浅橙色
    primaryDark: '#FF6600', // 深橙红色
    secondary: '#8B4513', // 马鞍棕色
    secondaryLight: '#CD853F', // 秘鲁色
    success: '#52C41A',
    warning: '#FAAD14',
    error: '#F5222D',
    info: '#1890FF',
    background: '#FFFAF0', // 花白色背景
    surface: '#FFFFFF',
    textPrimary: '#2F1B14', // 深棕色文字
    textSecondary: '#8B6914', // 暗金色
    textDisabled: '#D2B48C', // 棕褐色
    border: '#F4E4BC',
    divider: '#F9F1E6',
  },
}

/**
 * 墨玉主题 - 深邃神秘，低调奢华
 * 主色调采用墨绿色，体现墨玉的深沉与高贵
 */
export const inkJadeTheme: ThemeConfig = {
  name: 'inkJade',
  displayName: '墨玉雅黑',
  description: '深邃神秘，低调奢华的墨玉主题',
  colors: {
    primary: '#2F4F4F', // 深石板灰色，象征墨玉
    primaryLight: '#708090', // 石板灰色
    primaryDark: '#1C1C1C', // 深灰色
    secondary: '#C0C0C0', // 银色
    secondaryLight: '#E5E5E5', // 浅银色
    success: '#52C41A',
    warning: '#FAAD14',
    error: '#F5222D',
    info: '#1890FF',
    background: '#F8F8FF', // 幽灵白背景
    surface: '#FFFFFF',
    textPrimary: '#2C2C2C', // 深灰色文字
    textSecondary: '#696969', // 暗灰色
    textDisabled: '#A9A9A9', // 深灰色
    border: '#E0E0E0',
    divider: '#F5F5F5',
  },
}

/**
 * 所有可用主题
 */
export const themes: ThemeConfig[] = [jadeTheme, agarwoodTheme, amberTheme, inkJadeTheme]

/**
 * 默认主题
 */
export const defaultTheme = jadeTheme

/**
 * 根据主题名称获取主题配置
 */
export function getThemeByName(name: string): ThemeConfig {
  return themes.find((theme) => theme.name === name) || defaultTheme
}

/**
 * 获取主题的CSS变量对象
 */
export function getThemeCSSVars(theme: ThemeConfig): Record<string, string> {
  const { colors } = theme
  return {
    '--theme-primary': colors.primary,
    '--theme-primary-light': colors.primaryLight,
    '--theme-primary-dark': colors.primaryDark,
    '--theme-secondary': colors.secondary,
    '--theme-secondary-light': colors.secondaryLight,
    '--theme-success': colors.success,
    '--theme-warning': colors.warning,
    '--theme-error': colors.error,
    '--theme-info': colors.info,
    '--theme-background': colors.background,
    '--theme-surface': colors.surface,
    '--theme-text-primary': colors.textPrimary,
    '--theme-text-secondary': colors.textSecondary,
    '--theme-text-disabled': colors.textDisabled,
    '--theme-border': colors.border,
    '--theme-divider': colors.divider,
    // wot-design-uni 主题变量
    '--wot-color-theme': colors.primary,
    '--wot-button-primary-bg-color': colors.primary,
    '--wot-button-primary-color': '#FFFFFF',
    // uni-app 主题变量
    '--uni-color-primary': colors.primary,
    '--uni-text-color': colors.textPrimary,
    '--uni-bg-color': colors.background,
    '--uni-border-color': colors.border,
  }
}
