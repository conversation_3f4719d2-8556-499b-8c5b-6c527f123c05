<template>
  <view class="theme-switcher">
    <!-- 主题切换按钮 -->
    <wd-button
      v-if="!showPanel"
      type="primary"
      size="small"
      round
      @click="showPanel = true"
      class="theme-btn"
    >
      <wd-icon name="palette" size="16px" />
      主题
    </wd-button>

    <!-- 主题选择面板 -->
    <wd-popup
      v-model="showPanel"
      position="bottom"
      :safe-area-inset-bottom="true"
      custom-style="border-radius: 20rpx 20rpx 0 0;"
    >
      <view class="theme-panel">
        <view class="panel-header">
          <text class="panel-title">选择主题</text>
          <wd-icon name="close" size="20px" @click="showPanel = false" />
        </view>
        
        <view class="theme-list">
          <view
            v-for="theme in availableThemes"
            :key="theme.name"
            class="theme-item"
            :class="{ active: currentThemeName === theme.name }"
            @click="handleThemeChange(theme.name)"
          >
            <view class="theme-preview">
              <view
                class="color-primary"
                :style="{ backgroundColor: theme.colors.primary }"
              ></view>
              <view
                class="color-secondary"
                :style="{ backgroundColor: theme.colors.secondary }"
              ></view>
              <view
                class="color-background"
                :style="{ backgroundColor: theme.colors.background }"
              ></view>
            </view>
            
            <view class="theme-info">
              <text class="theme-name">{{ theme.displayName }}</text>
              <text class="theme-desc">{{ theme.description }}</text>
            </view>
            
            <wd-icon
              v-if="currentThemeName === theme.name"
              name="check"
              size="20px"
              color="var(--theme-primary)"
            />
          </view>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { useThemeStore } from '@/store/theme'

interface Props {
  /** 是否显示为浮动按钮 */
  floating?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  floating: true,
})

const themeStore = useThemeStore()

// 控制面板显示
const showPanel = ref(false)

// 当前主题名称
const currentThemeName = computed(() => themeStore.currentThemeName)

// 可用主题列表
const availableThemes = computed(() => themeStore.availableThemes)

/**
 * 处理主题切换
 */
const handleThemeChange = async (themeName: string) => {
  await themeStore.setTheme(themeName)
  showPanel.value = false
  
  // 触发震动反馈
  uni.vibrateShort({
    type: 'light'
  })
}
</script>

<style lang="scss" scoped>
.theme-switcher {
  .theme-btn {
    position: fixed;
    bottom: 200rpx;
    right: 40rpx;
    z-index: 999;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  }
}

.theme-panel {
  padding: 40rpx;
  background: var(--theme-surface);
  
  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40rpx;
    
    .panel-title {
      font-size: 36rpx;
      font-weight: 600;
      color: var(--theme-text-primary);
    }
  }
  
  .theme-list {
    .theme-item {
      display: flex;
      align-items: center;
      padding: 30rpx 20rpx;
      margin-bottom: 20rpx;
      border-radius: 20rpx;
      background: var(--theme-background);
      border: 2rpx solid transparent;
      transition: all 0.3s ease;
      
      &.active {
        border-color: var(--theme-primary);
        background: var(--theme-primary-light, var(--theme-primary));
        background: color-mix(in srgb, var(--theme-primary) 10%, transparent);
      }
      
      .theme-preview {
        display: flex;
        margin-right: 30rpx;
        
        .color-primary,
        .color-secondary,
        .color-background {
          width: 40rpx;
          height: 40rpx;
          border-radius: 50%;
          margin-right: 10rpx;
          border: 2rpx solid rgba(255, 255, 255, 0.8);
        }
        
        .color-background {
          border-color: var(--theme-border);
        }
      }
      
      .theme-info {
        flex: 1;
        
        .theme-name {
          display: block;
          font-size: 32rpx;
          font-weight: 500;
          color: var(--theme-text-primary);
          margin-bottom: 8rpx;
        }
        
        .theme-desc {
          font-size: 24rpx;
          color: var(--theme-text-secondary);
          line-height: 1.4;
        }
      }
    }
  }
}
</style>
