<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5" type="home">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '首页',
  },
}
</route>
<template>
  <custom-navbar title="奢侈品商城"></custom-navbar>
  <view class="page-container" :style="{ marginTop: safeAreaInsets?.top + 'px' }">
    <wd-swiper
      :list="swiperList"
      autoplay
      v-model:current="current"
      @click="handleSwiperClick"
      @onChange="onSwiperChange"
    ></wd-swiper>
  </view>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { useThemeStore } from '@/store/theme'

defineOptions({
  name: 'Home',
})

const themeStore = useThemeStore()

// 获取屏幕边界到安全区域距离
const { safeAreaInsets } = uni.getSystemInfoSync()

// 当前主题
const currentTheme = computed(() => themeStore.currentTheme)

// 测试 uni API 自动引入
onLoad(() => {
  console.log('奢侈品商城首页加载完成')
  console.log('当前主题:', currentTheme.value.displayName)
})

const current = ref<number>(0)
const swiperList = ref([
  'https://readdy.ai/api/search-image?query=Luxury%20jade%20jewelry%20display%20with%20elegant%20lighting%2C%20premium%20jade%20bracelet%20and%20necklace%20arrangement%20on%20silk%20cloth%2C%20soft%20shadows%2C%20professional%20product%20photography%2C%20isolated%20on%20cream%20background%2C%20centered%20composition%2C%20high%20detail%20quality&width=750&height=350&seq=1&orientation=landscape',
  'https://readdy.ai/api/search-image?query=Premium%20agarwood%20incense%20display%2C%20luxury%20dark%20wood%20chunks%20with%20subtle%20smoke%2C%20artistic%20lighting%20on%20black%20marble%20surface%2C%20professional%20product%20photography%2C%20centered%20composition%2C%20high%20detail%20quality%2C%20soft%20shadows&width=750&height=350&seq=2&orientation=landscape',
  'https://readdy.ai/api/search-image?query=Luxury%20jewelry%20collection%20featuring%20jade%20and%20gold%20pieces%2C%20elegant%20arrangement%20on%20dark%20velvet%2C%20professional%20lighting%2C%20premium%20product%20photography%2C%20centered%20composition%2C%20high%20detail%20quality%2C%20soft%20shadows&width=750&height=350&seq=3&orientation=landscape',
])

// 分类导航数据
const categories = ref([
  {
    id: 1,
    name: '沉香',
    icon: 'https://readdy.ai/api/search-image?query=icon%2C%203D%20vector%20illustration%2C%20agarwood%20incense%2C%20high-quality%20details%2C%20prominent%20main%20subject%2C%20clear%20and%20sharp%2C%20subject%20fills%2080%20percent%20of%20frame%2C%20isolated%20on%20white%20background%2C%20centered%20composition%2C%20soft%20lighting%2C%20no%20shadows%2C%20no%20text%2C%20Content%20simple&width=100&height=100&seq=4&orientation=squarish',
    description: '天然沉香，香韵悠长',
  },
  {
    id: 2,
    name: '玉器',
    icon: 'https://readdy.ai/api/search-image?query=icon%2C%203D%20vector%20illustration%2C%20jade%20bracelet%2C%20high-quality%20details%2C%20prominent%20main%20subject%2C%20clear%20and%20sharp%2C%20subject%20fills%2080%20percent%20of%20frame%2C%20isolated%20on%20white%20background%2C%20centered%20composition%2C%20soft%20lighting%2C%20no%20shadows%2C%20no%20text%2C%20Content%20simple&width=100&height=100&seq=5&orientation=squarish',
    description: '温润如玉，品质上乘',
  },
  {
    id: 3,
    name: '珠宝',
    icon: 'https://readdy.ai/api/search-image?query=icon%2C%203D%20vector%20illustration%2C%20diamond%20necklace%2C%20high-quality%20details%2C%20prominent%20main%20subject%2C%20clear%20and%20sharp%2C%20subject%20fills%2080%20percent%20of%20frame%2C%20isolated%20on%20white%20background%2C%20centered%20composition%2C%20soft%20lighting%2C%20no%20shadows%2C%20no%20text%2C%20Content%20simple&width=100&height=100&seq=6&orientation=squarish',
    description: '璀璨夺目，奢华典雅',
  },
  {
    id: 4,
    name: '鉴定',
    icon: 'https://readdy.ai/api/search-image?query=icon%2C%203D%20vector%20illustration%2C%20magnifying%20glass%20examining%20gemstone%2C%20high-quality%20details%2C%20prominent%20main%20subject%2C%20clear%20and%20sharp%2C%20subject%20fills%2080%20percent%20of%20frame%2C%20isolated%20on%20white%20background%2C%20centered%20composition%2C%20soft%20lighting%2C%20no%20shadows%2C%20no%20text%2C%20Content%20simple&width=100&height=100&seq=7&orientation=squarish',
    description: '专业鉴定，权威认证',
  },
  {
    id: 5,
    name: '文玩',
    icon: 'https://readdy.ai/api/search-image?query=icon%2C%203D%20vector%20illustration%2C%20wooden%20prayer%20beads%2C%20high-quality%20details%2C%20prominent%20main%20subject%2C%20clear%20and%20sharp%2C%20subject%20fills%2080%20percent%20of%20frame%2C%20isolated%20on%20white%20background%2C%20centered%20composition%2C%20soft%20lighting%2C%20no%20shadows%2C%20no%20text%2C%20Content%20simple&width=100&height=100&seq=8&orientation=squarish',
    description: '传统文玩，文化传承',
  },
  ,
  {
    id: 6,
    name: '茶具',
    icon: 'https://readdy.ai/api/search-image?query=icon%2C%203D%20vector%20illustration%2C%20traditional%20Chinese%20tea%20set%2C%20high-quality%20details%2C%20prominent%20main%20subject%2C%20clear%20and%20sharp%2C%20subject%20fills%2080%20percent%20of%20frame%2C%20isolated%20on%20white%20background%2C%20centered%20composition%2C%20soft%20lighting%2C%20no%20shadows%2C%20no%20text%2C%20Content%20simple&width=100&height=100&seq=9&orientation=squarish',
    description: '精美茶具，品茗雅器',
  },
  {
    id: 7,
    name: '摆件',
    icon: 'https://readdy.ai/api/search-image?query=icon%2C%203D%20vector%20illustration%2C%20jade%20Buddha%20statue%2C%20high-quality%20details%2C%20prominent%20main%20subject%2C%20clear%20and%20sharp%2C%20subject%20fills%2080%20percent%20of%20frame%2C%20isolated%20on%20white%20background%2C%20centered%20composition%2C%20soft%20lighting%2C%20no%20shadows%2C%20no%20text%2C%20Content%20simple&width=100&height=100&seq=10&orientation=squarish',
    description: '艺术摆件，装饰精品',
  },
  {
    id: 8,
    name: '全部',
    icon: 'https://readdy.ai/api/search-image?query=icon%2C%203D%20vector%20illustration%2C%20grid%20of%20luxury%20items%2C%20high-quality%20details%2C%20prominent%20main%20subject%2C%20clear%20and%20sharp%2C%20subject%20fills%2080%20percent%20of%20frame%2C%20isolated%20on%20white%20background%2C%20centered%20composition%2C%20soft%20lighting%2C%20no%20shadows%2C%20no%20text%2C%20Content%20simple&width=100&height=100&seq=11&orientation=squarish',
    description: '查看全部分类',
  },
])

// 根据分类数量自动分行的计算属性
const categoryRows = computed(() => {
  const rows = []
  const itemsPerRow = 4

  for (let i = 0; i < categories.value.length; i += itemsPerRow) {
    rows.push(categories.value.slice(i, i + itemsPerRow))
  }

  return rows
})

function handleSwiperClick(index: number) {
  console.log('点击了轮播图', index)
}

function onSwiperChange(index: number) {
  console.log('轮播图切换了', index)
}

function handleCategoryClick(category: any, index: number) {
  console.log('点击了分类', category.name, index)
  // 这里可以跳转到对应的分类页面
  uni.showToast({
    title: `进入${category.name}分类`,
    icon: 'none',
  })
}
</script>

<style lang="scss" scoped>
/* 分类导航 */
.category-section {
  margin: 20rpx;
  background-color: var(--theme-surface, #ffffff);
  border-radius: 16rpx;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.category-title {
  padding: 30rpx 30rpx 20rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: var(--theme-text-primary);
  text-align: center;
  border-bottom: 1rpx solid var(--theme-divider, #f0f0f0);
}

.category-container {
  padding: 20rpx;
}

.category-row {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 20rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.category-item {
  flex: 1;
  max-width: 25%;
  padding: 20rpx 10rpx;
  transition: all 0.3s ease;
  border-radius: 12rpx;
  cursor: pointer;

  &:active {
    background-color: var(--theme-background, #f5f5f5);
    transform: scale(0.95);
  }

  &:hover {
    background-color: rgba(var(--theme-primary), 0.05);
  }
}

.category-empty {
  visibility: hidden;
}

.category-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.category-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 12rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.category-name {
  font-size: 24rpx;
  color: var(--theme-text-primary, #333);
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
}

.page-container {
  min-height: 100vh;
  background-color: var(--theme-background);
  padding: 40rpx;
}

.welcome-section {
  text-align: center;
  margin-bottom: 60rpx;

  .welcome-title {
    font-size: 48rpx;
    font-weight: 600;
    color: var(--theme-text-primary);
    margin-bottom: 20rpx;
  }

  .welcome-subtitle {
    font-size: 28rpx;
    color: var(--theme-text-secondary);
  }
}

.theme-showcase {
  background: var(--theme-surface);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

  .section-title {
    font-size: 32rpx;
    font-weight: 500;
    color: var(--theme-text-primary);
    margin-bottom: 30rpx;
    text-align: center;
  }

  .color-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30rpx;

    .color-item {
      display: flex;
      flex-direction: column;
      align-items: center;

      .color-box {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        margin-bottom: 16rpx;
        border: 4rpx solid rgba(255, 255, 255, 0.8);
        box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
      }

      .color-name {
        font-size: 24rpx;
        color: var(--theme-text-secondary);
      }
    }
  }
}

.primary-color {
  background-color: var(--theme-primary) !important;
}

.secondary-color {
  background-color: var(--theme-secondary) !important;
}

.success-color {
  background-color: var(--theme-success) !important;
}

.warning-color {
  background-color: var(--theme-warning) !important;
}

.action-section {
  margin-bottom: 40rpx;

  .action-btn {
    margin-bottom: 20rpx;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.theme-info {
  background: var(--theme-surface);
  border-radius: 20rpx;
  padding: 40rpx;
  border: 2rpx solid var(--theme-border);

  .info-title {
    font-size: 32rpx;
    font-weight: 500;
    color: var(--theme-text-primary);
    margin-bottom: 20rpx;
  }

  .info-content {
    .theme-name {
      display: block;
      font-size: 28rpx;
      font-weight: 500;
      color: var(--theme-primary);
      margin-bottom: 12rpx;
    }

    .theme-desc {
      font-size: 24rpx;
      color: var(--theme-text-secondary);
      line-height: 1.5;
    }
  }
}
</style>
