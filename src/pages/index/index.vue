<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5" type="home">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '首页',
  },
}
</route>
<template>
  <custom-navbar title="奢侈品商城"></custom-navbar>
  <view class="page-container" :style="{ marginTop: safeAreaInsets?.top + 'px' }">
    <!-- 欢迎区域 -->
    <view class="welcome-section">
      <view class="welcome-title">欢迎来到奢侈品商城</view>
      <view class="welcome-subtitle">精选玉石、沉香、琥珀等珍品</view>
    </view>

    <!-- 主题展示区域 -->
    <view class="theme-showcase">
      <view class="section-title">主题色彩展示</view>
      <view class="color-grid">
        <view class="color-item">
          <view class="color-box primary-color"></view>
          <text class="color-name">主色调</text>
        </view>
        <view class="color-item">
          <view class="color-box secondary-color"></view>
          <text class="color-name">辅助色</text>
        </view>
        <view class="color-item">
          <view class="color-box success-color"></view>
          <text class="color-name">成功色</text>
        </view>
        <view class="color-item">
          <view class="color-box warning-color"></view>
          <text class="color-name">警告色</text>
        </view>
      </view>
    </view>

    <!-- 功能按钮区域 -->
    <view class="action-section">
      <wd-button type="primary" block class="action-btn">浏览商品</wd-button>
      <wd-button type="default" block class="action-btn">查看收藏</wd-button>
    </view>

    <!-- 当前主题信息 -->
    <view class="theme-info">
      <view class="info-title">当前主题</view>
      <view class="info-content">
        <text class="theme-name">{{ currentTheme.displayName }}</text>
        <text class="theme-desc">{{ currentTheme.description }}</text>
      </view>
    </view>

    <!-- 主题切换组件 -->
    <theme-switcher />
  </view>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { useThemeStore } from '@/store/theme'
import ThemeSwitcher from '@/components/theme-switcher/theme-switcher.vue'

defineOptions({
  name: 'Home',
})

const themeStore = useThemeStore()

// 获取屏幕边界到安全区域距离
const { safeAreaInsets } = uni.getSystemInfoSync()

// 当前主题
const currentTheme = computed(() => themeStore.currentTheme)

// 测试 uni API 自动引入
onLoad(() => {
  console.log('奢侈品商城首页加载完成')
  console.log('当前主题:', currentTheme.value.displayName)
})
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background-color: var(--theme-background);
  padding: 40rpx;
}

.welcome-section {
  text-align: center;
  margin-bottom: 60rpx;

  .welcome-title {
    font-size: 48rpx;
    font-weight: 600;
    color: var(--theme-text-primary);
    margin-bottom: 20rpx;
  }

  .welcome-subtitle {
    font-size: 28rpx;
    color: var(--theme-text-secondary);
  }
}

.theme-showcase {
  background: var(--theme-surface);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

  .section-title {
    font-size: 32rpx;
    font-weight: 500;
    color: var(--theme-text-primary);
    margin-bottom: 30rpx;
    text-align: center;
  }

  .color-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30rpx;

    .color-item {
      display: flex;
      flex-direction: column;
      align-items: center;

      .color-box {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        margin-bottom: 16rpx;
        border: 4rpx solid rgba(255, 255, 255, 0.8);
        box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
      }

      .color-name {
        font-size: 24rpx;
        color: var(--theme-text-secondary);
      }
    }
  }
}

.primary-color {
  background-color: var(--theme-primary) !important;
}

.secondary-color {
  background-color: var(--theme-secondary) !important;
}

.success-color {
  background-color: var(--theme-success) !important;
}

.warning-color {
  background-color: var(--theme-warning) !important;
}

.action-section {
  margin-bottom: 40rpx;

  .action-btn {
    margin-bottom: 20rpx;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.theme-info {
  background: var(--theme-surface);
  border-radius: 20rpx;
  padding: 40rpx;
  border: 2rpx solid var(--theme-border);

  .info-title {
    font-size: 32rpx;
    font-weight: 500;
    color: var(--theme-text-primary);
    margin-bottom: 20rpx;
  }

  .info-content {
    .theme-name {
      display: block;
      font-size: 28rpx;
      font-weight: 500;
      color: var(--theme-primary);
      margin-bottom: 12rpx;
    }

    .theme-desc {
      font-size: 24rpx;
      color: var(--theme-text-secondary);
      line-height: 1.5;
    }
  }
}
</style>
