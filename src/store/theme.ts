import { defineStore } from 'pinia'
import { ref, computed, nextTick } from 'vue'
import type { ThemeConfig } from '@/config/theme'
import { defaultTheme, getThemeByName, getThemeCSSVars, themes } from '@/config/theme'

/**
 * 主题状态管理
 */
export const useThemeStore = defineStore(
  'theme',
  () => {
    // 当前主题名称
    const currentThemeName = ref<string>(defaultTheme.name)
    
    // 当前主题配置
    const currentTheme = computed<ThemeConfig>(() => {
      return getThemeByName(currentThemeName.value)
    })
    
    // 当前主题的CSS变量
    const currentThemeVars = computed(() => {
      return getThemeCSSVars(currentTheme.value)
    })
    
    // 所有可用主题
    const availableThemes = computed(() => themes)
    
    /**
     * 设置主题
     * @param themeName 主题名称
     */
    const setTheme = async (themeName: string) => {
      const theme = getThemeByName(themeName)
      if (!theme) {
        console.warn(`主题 "${themeName}" 不存在，使用默认主题`)
        return
      }
      
      currentThemeName.value = themeName
      
      // 等待下一个tick，确保响应式数据已更新
      await nextTick()
      
      // 应用CSS变量到根元素
      applyThemeVars()
      
      console.log(`已切换到主题: ${theme.displayName}`)
    }
    
    /**
     * 应用主题CSS变量到根元素
     */
    const applyThemeVars = () => {
      const vars = currentThemeVars.value
      
      // 在H5环境下应用到document.documentElement
      // #ifdef H5
      if (typeof document !== 'undefined') {
        const root = document.documentElement
        Object.entries(vars).forEach(([key, value]) => {
          root.style.setProperty(key, value)
        })
      }
      // #endif
      
      // 在小程序环境下，CSS变量会通过组件的style属性应用
      // 这里主要是为了触发响应式更新
    }
    
    /**
     * 重置为默认主题
     */
    const resetTheme = () => {
      setTheme(defaultTheme.name)
    }
    
    /**
     * 获取主题预览色彩
     * @param themeName 主题名称
     */
    const getThemePreview = (themeName: string) => {
      const theme = getThemeByName(themeName)
      return {
        primary: theme.colors.primary,
        secondary: theme.colors.secondary,
        background: theme.colors.background,
      }
    }
    
    /**
     * 初始化主题
     */
    const initTheme = () => {
      // 应用当前主题的CSS变量
      applyThemeVars()
    }
    
    return {
      // 状态
      currentThemeName,
      currentTheme,
      currentThemeVars,
      availableThemes,
      
      // 方法
      setTheme,
      resetTheme,
      getThemePreview,
      initTheme,
      applyThemeVars,
    }
  },
  {
    // 持久化配置
    persist: {
      key: 'theme-store',
      storage: {
        getItem: (key: string) => uni.getStorageSync(key),
        setItem: (key: string, value: string) => uni.setStorageSync(key, value),
      },
      // 只持久化主题名称
      paths: ['currentThemeName'],
    },
  },
)
