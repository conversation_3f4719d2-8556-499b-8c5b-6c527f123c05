<template>
  <wd-config-provider :themeVars="themeVars" :style="rootStyle">
    <slot />
    <wd-toast />
    <wd-message-box />
    <privacy-popup />
  </wd-config-provider>
</template>

<script lang="ts" setup>
import { computed, onMounted } from 'vue'
import type { ConfigProviderThemeVars } from 'wot-design-uni'
import { useThemeStore } from '@/store/theme'

const themeStore = useThemeStore()

// wot-design-uni 主题变量
const themeVars = computed<ConfigProviderThemeVars>(() => {
  const { colors } = themeStore.currentTheme
  return {
    colorTheme: colors.primary,
    buttonPrimaryBgColor: colors.primary,
    buttonPrimaryColor: '#FFFFFF',
    // 可以根据需要添加更多wot-design-uni的主题变量
  }
})

// 根元素样式，应用CSS变量
const rootStyle = computed(() => {
  return themeStore.currentThemeVars
})

// 初始化主题
onMounted(() => {
  themeStore.initTheme()
})
</script>
