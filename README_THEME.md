# 奢侈品电商小程序主题系统

## 🎨 项目概述

为玉石、沉香等奢侈品电商小程序设计的完整主题系统，支持动态切换主题色，提供4套精心设计的主题方案。

## ✨ 主题方案

### 1. 翡翠玉石主题 (jade) - 默认主题
- **主色调**: #2E8B57 (海绿色)
- **辅助色**: #D4AF37 (金色)
- **特点**: 温润如玉，高雅内敛
- **适用**: 玉石类商品展示

### 2. 沉香雅韵主题 (agarwood)
- **主色调**: #8B4513 (马鞍棕色)
- **辅助色**: #B8860B (暗金色)
- **特点**: 深沉内敛，古典雅致
- **适用**: 沉香类商品展示

### 3. 琥珀流光主题 (amber)
- **主色调**: #FF8C00 (深橙色)
- **辅助色**: #8B4513 (马鞍棕色)
- **特点**: 温暖明亮，奢华典雅
- **适用**: 琥珀类商品展示

### 4. 墨玉雅黑主题 (inkJade)
- **主色调**: #2F4F4F (深石板灰色)
- **辅助色**: #C0C0C0 (银色)
- **特点**: 深邃神秘，低调奢华
- **适用**: 墨玉类商品展示

## 🚀 功能特性

- ✅ **动态主题切换**: 实时切换主题，无需刷新页面
- ✅ **持久化存储**: 自动保存用户选择的主题
- ✅ **响应式设计**: 支持多端适配（H5、小程序、APP）
- ✅ **组件库集成**: 自动同步 wot-design-uni 主题变量
- ✅ **CSS变量支持**: 完整的CSS变量系统
- ✅ **UnoCSS集成**: 支持原子化CSS类名
- ✅ **TypeScript支持**: 完整的类型定义

## 📁 文件结构

```
src/
├── config/
│   └── theme.ts              # 主题配置文件
├── store/
│   └── theme.ts              # 主题状态管理
├── components/
│   └── theme-switcher/       # 主题切换组件
│       └── theme-switcher.vue
├── layouts/
│   └── default.vue           # 布局文件（已集成主题）
└── style/
    └── index.scss            # 全局样式（包含主题变量）
```

## 🎯 使用方法

### 1. 在组件中使用主题

#### 使用CSS变量
```scss
.my-component {
  color: var(--theme-primary);
  background-color: var(--theme-surface);
  border-color: var(--theme-border);
}
```

#### 使用UnoCSS类名
```html
<view class="text-primary bg-surface border-border">
  使用主题色的内容
</view>
```

#### 使用预定义的样式类
```html
<view class="theme-primary-bg theme-text-primary">
  使用主题背景和文字色
</view>
```

### 2. 在JavaScript中使用主题

```typescript
import { useThemeStore } from '@/store/theme'

const themeStore = useThemeStore()

// 获取当前主题
const currentTheme = themeStore.currentTheme

// 切换主题
await themeStore.setTheme('agarwood')

// 获取主题预览色彩
const preview = themeStore.getThemePreview('amber')
```

### 3. 添加主题切换组件

```vue
<template>
  <view>
    <!-- 你的页面内容 -->
    
    <!-- 添加主题切换组件 -->
    <theme-switcher />
  </view>
</template>

<script setup>
import ThemeSwitcher from '@/components/theme-switcher/theme-switcher.vue'
</script>
```

## 🎨 可用的CSS变量

### 颜色变量
- `--theme-primary`: 主色调
- `--theme-primary-light`: 主色调浅色
- `--theme-primary-dark`: 主色调深色
- `--theme-secondary`: 辅助色
- `--theme-secondary-light`: 辅助色浅色
- `--theme-success`: 成功色
- `--theme-warning`: 警告色
- `--theme-error`: 错误色
- `--theme-info`: 信息色
- `--theme-background`: 背景色
- `--theme-surface`: 表面色
- `--theme-text-primary`: 文字主色
- `--theme-text-secondary`: 文字次要色
- `--theme-text-disabled`: 文字禁用色
- `--theme-border`: 边框色
- `--theme-divider`: 分割线色

## 🔧 技术实现

- **状态管理**: 使用 Pinia 管理主题状态
- **持久化**: 使用 pinia-plugin-persistedstate 自动保存主题选择
- **样式系统**: 结合 SCSS、UnoCSS 和 CSS 变量
- **组件库集成**: 自动同步 wot-design-uni 主题变量
- **响应式**: 主题切换实时生效，无需刷新页面

## 📱 演示页面

- **首页**: 展示主题色彩和切换功能
- **我的页面**: 集成主题设置入口和切换组件

## 🎉 快速开始

1. 启动项目
```bash
pnpm dev:h5
```

2. 访问 http://localhost:9001

3. 点击右下角的主题按钮体验主题切换功能

## 📖 详细文档

更多详细信息请查看 [主题系统使用指南](./docs/THEME_GUIDE.md)

## 🎨 设计理念

每个主题都经过精心设计，体现了不同奢侈品的特色：
- **翡翠玉石**: 温润如玉的绿色调，体现玉石的高雅
- **沉香雅韵**: 深沉的棕色调，体现沉香的古典
- **琥珀流光**: 温暖的橙色调，体现琥珀的明亮
- **墨玉雅黑**: 神秘的灰色调，体现墨玉的深邃

## 🔮 未来规划

- [ ] 添加更多主题方案
- [ ] 支持自定义主题创建
- [ ] 添加主题预览功能
- [ ] 支持渐变色主题
- [ ] 添加暗黑模式支持
