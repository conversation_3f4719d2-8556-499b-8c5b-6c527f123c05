# 分类模块实现说明

## 📋 功能概述

实现了一个根据 categories 数组长度自动调整布局的分类模块，支持：
- 每行显示 4 个分类
- 根据分类数量自动分行
- 响应式布局，适配不同屏幕尺寸
- 主题色彩集成

## 🎯 布局规则

### 自动分行逻辑
- **1-4 个分类**: 显示为 1 行
- **5-8 个分类**: 显示为 2 行
- **9-12 个分类**: 显示为 3 行
- **以此类推**: 每 4 个分类为一行

### 当前分类数据
项目中包含 8 个分类，自动分为 2 行：

**第一行 (4个)**:
1. 沉香 - 天然沉香，香韵悠长
2. 玉器 - 温润如玉，品质上乘
3. 珠宝 - 璀璨夺目，奢华典雅
4. 鉴定 - 专业鉴定，权威认证

**第二行 (4个)**:
5. 文玩 - 传统文玩，文化传承
6. 茶具 - 精美茶具，品茗雅器
7. 摆件 - 艺术摆件，装饰精品
8. 全部 - 查看全部分类

## 🔧 技术实现

### 1. 数据结构
```typescript
const categories = ref([
  {
    id: 1,
    name: '沉香',
    icon: 'https://...',
    description: '天然沉香，香韵悠长',
  },
  // ... 更多分类
])
```

### 2. 自动分行计算属性
```typescript
const categoryRows = computed(() => {
  const rows = []
  const itemsPerRow = 4
  
  for (let i = 0; i < categories.value.length; i += itemsPerRow) {
    rows.push(categories.value.slice(i, i + itemsPerRow))
  }
  
  return rows
})
```

### 3. 模板结构
```vue
<view class="category-section">
  <view class="category-title">商品分类</view>
  <view class="category-container">
    <view 
      v-for="(row, rowIndex) in categoryRows" 
      :key="rowIndex" 
      class="category-row"
    >
      <view
        v-for="(category, colIndex) in row"
        :key="category.id"
        class="category-item"
        @click="handleCategoryClick(category, rowIndex * 4 + colIndex)"
      >
        <view class="category-content">
          <image :src="category.icon" mode="aspectFill" class="category-icon"></image>
          <text class="category-name">{{ category.name }}</text>
        </view>
      </view>
      <!-- 填充空白位置 -->
      <view 
        v-for="emptyIndex in 4 - row.length" 
        :key="`empty-${rowIndex}-${emptyIndex}`"
        class="category-item category-empty"
      ></view>
    </view>
  </view>
</view>
```

## 🎨 样式特性

### 响应式布局
- 使用 Flexbox 布局
- 每个分类项占 25% 宽度
- 自动填充空白位置

### 主题集成
- 背景色: `var(--theme-surface)`
- 文字色: `var(--theme-text-primary)`
- 分割线: `var(--theme-divider)`
- 悬停效果: `var(--theme-primary)` 透明度

### 交互效果
- 点击缩放动画
- 悬停背景变化
- 平滑过渡效果

## 📱 位置布局

分类模块位于首页轮播图下方：
```
┌─────────────────┐
│   自定义导航栏    │
├─────────────────┤
│     轮播图       │
├─────────────────┤
│   分类模块       │ ← 新增位置
│  ┌──┬──┬──┬──┐  │
│  │沉│玉│珠│鉴│  │
│  │香│器│宝│定│  │
│  ├──┼──┼──┼──┤  │
│  │文│茶│摆│全│  │
│  │玩│具│件│部│  │
│  └──┴──┴──┴──┘  │
├─────────────────┤
│    其他内容      │
└─────────────────┘
```

## 🔄 动态扩展

### 添加新分类
只需在 categories 数组中添加新项：
```typescript
categories.value.push({
  id: 9,
  name: '新分类',
  icon: 'https://...',
  description: '新分类描述',
})
```

### 自动适配
- 9个分类 → 自动变为3行 (4+4+1)
- 12个分类 → 自动变为3行 (4+4+4)
- 13个分类 → 自动变为4行 (4+4+4+1)

## 🎯 点击事件

```typescript
function handleCategoryClick(category: any, index: number) {
  console.log('点击了分类', category.name, index)
  uni.showToast({
    title: `进入${category.name}分类`,
    icon: 'none'
  })
  // 可以在这里添加跳转逻辑
  // uni.navigateTo({ url: `/pages/category/${category.id}` })
}
```

## 🚀 使用优势

1. **自适应布局**: 无需手动调整行数
2. **主题集成**: 自动适配当前主题色彩
3. **响应式设计**: 支持不同屏幕尺寸
4. **易于维护**: 只需修改数据数组
5. **性能优化**: 使用计算属性缓存分行结果

## 📝 测试验证

访问 http://localhost:9000 可以看到：
1. 轮播图下方显示分类模块
2. 8个分类自动分为2行显示
3. 每行4个分类，布局整齐
4. 点击分类有交互反馈
5. 主题切换时颜色自动更新

## 🔮 扩展建议

1. **图标优化**: 可以使用本地图标或字体图标
2. **加载状态**: 添加分类数据加载中的骨架屏
3. **懒加载**: 对分类图标实现懒加载
4. **动画效果**: 添加分类项的进入动画
5. **无障碍**: 添加 aria-label 等无障碍属性
