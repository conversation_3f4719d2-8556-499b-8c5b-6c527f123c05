# 主题系统使用指南

## 概述

本项目为玉石、沉香等奢侈品电商小程序设计了一套完整的主题系统，支持动态切换主题色，提供了4套精心设计的主题方案。

## 主题方案

### 1. 翡翠玉石主题 (jade)
- **主色调**: #2E8B57 (海绿色)
- **辅助色**: #D4AF37 (金色)
- **特点**: 温润如玉，高雅内敛
- **适用**: 玉石类商品展示

### 2. 沉香雅韵主题 (agarwood)
- **主色调**: #8B4513 (马鞍棕色)
- **辅助色**: #B8860B (暗金色)
- **特点**: 深沉内敛，古典雅致
- **适用**: 沉香类商品展示

### 3. 琥珀流光主题 (amber)
- **主色调**: #FF8C00 (深橙色)
- **辅助色**: #8B4513 (马鞍棕色)
- **特点**: 温暖明亮，奢华典雅
- **适用**: 琥珀类商品展示

### 4. 墨玉雅黑主题 (inkJade)
- **主色调**: #2F4F4F (深石板灰色)
- **辅助色**: #C0C0C0 (银色)
- **特点**: 深邃神秘，低调奢华
- **适用**: 墨玉类商品展示

## 文件结构

```
src/
├── config/
│   └── theme.ts              # 主题配置文件
├── store/
│   └── theme.ts              # 主题状态管理
├── components/
│   └── theme-switcher/       # 主题切换组件
│       └── theme-switcher.vue
├── layouts/
│   └── default.vue           # 布局文件（已集成主题）
└── style/
    └── index.scss            # 全局样式（包含主题变量）
```

## 使用方法

### 1. 在组件中使用主题

#### 使用CSS变量
```scss
.my-component {
  color: var(--theme-primary);
  background-color: var(--theme-surface);
  border-color: var(--theme-border);
}
```

#### 使用UnoCSS类名
```html
<view class="text-primary bg-surface border-border">
  使用主题色的内容
</view>
```

#### 使用预定义的样式类
```html
<view class="theme-primary-bg theme-text-primary">
  使用主题背景和文字色
</view>
```

### 2. 在JavaScript中使用主题

```typescript
import { useThemeStore } from '@/store/theme'

const themeStore = useThemeStore()

// 获取当前主题
const currentTheme = themeStore.currentTheme

// 切换主题
await themeStore.setTheme('agarwood')

// 获取主题预览色彩
const preview = themeStore.getThemePreview('amber')
```

### 3. 添加主题切换组件

```vue
<template>
  <view>
    <!-- 你的页面内容 -->
    
    <!-- 添加主题切换组件 -->
    <theme-switcher />
  </view>
</template>

<script setup>
import ThemeSwitcher from '@/components/theme-switcher/theme-switcher.vue'
</script>
```

## 可用的CSS变量

### 颜色变量
- `--theme-primary`: 主色调
- `--theme-primary-light`: 主色调浅色
- `--theme-primary-dark`: 主色调深色
- `--theme-secondary`: 辅助色
- `--theme-secondary-light`: 辅助色浅色
- `--theme-success`: 成功色
- `--theme-warning`: 警告色
- `--theme-error`: 错误色
- `--theme-info`: 信息色
- `--theme-background`: 背景色
- `--theme-surface`: 表面色
- `--theme-text-primary`: 文字主色
- `--theme-text-secondary`: 文字次要色
- `--theme-text-disabled`: 文字禁用色
- `--theme-border`: 边框色
- `--theme-divider`: 分割线色

### 组件库变量
- `--wot-color-theme`: wot-design-uni主题色
- `--wot-button-primary-bg-color`: 按钮背景色
- `--uni-color-primary`: uni-app主题色

## 自定义主题

### 1. 添加新主题

在 `src/config/theme.ts` 中添加新的主题配置：

```typescript
export const customTheme: ThemeConfig = {
  name: 'custom',
  displayName: '自定义主题',
  description: '这是一个自定义主题',
  colors: {
    primary: '#your-color',
    // ... 其他颜色配置
  },
}

// 添加到主题列表
export const themes: ThemeConfig[] = [
  jadeTheme,
  agarwoodTheme,
  amberTheme,
  inkJadeTheme,
  customTheme, // 新增主题
]
```

### 2. 修改现有主题

直接修改 `src/config/theme.ts` 中对应主题的颜色值即可。

## 最佳实践

1. **优先使用CSS变量**: 使用 `var(--theme-primary)` 而不是硬编码颜色值
2. **提供回退值**: `var(--theme-primary, #2E8B57)` 确保在变量未定义时有默认值
3. **语义化命名**: 使用 `--theme-primary` 而不是 `--green-color`
4. **保持一致性**: 在整个应用中统一使用主题变量

## 注意事项

1. 主题切换会自动保存到本地存储，下次启动应用时会恢复上次选择的主题
2. 在小程序环境下，CSS变量的支持可能有限，建议测试各平台兼容性
3. 主题切换时会有轻微的震动反馈，提升用户体验
4. 建议在关键页面都添加主题切换组件，方便用户随时切换

## 技术实现

- **状态管理**: 使用 Pinia 管理主题状态
- **持久化**: 使用 pinia-plugin-persistedstate 自动保存主题选择
- **样式系统**: 结合 SCSS、UnoCSS 和 CSS 变量
- **组件库集成**: 自动同步 wot-design-uni 主题变量
- **响应式**: 主题切换实时生效，无需刷新页面
